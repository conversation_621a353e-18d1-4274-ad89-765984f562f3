#[cfg(test)]
mod cli_version_tests {
    use super::*;

    fn create_test_cli_version(version: &str) -> CliVersion {
        CliVersion {
            version: version.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            change_log: "Initial release".to_string(),
            minimal_supported: "1.0.0".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        let result = db.insert_cli_version(cli_version_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "1.0.0");
        
        // Verify CLI version was inserted
        assert!(db.cli_versions.contains(&"1.0.0".to_string()));
    }

    #[glue::test]
    fn test_insert_cli_version_duplicate() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert first time - should succeed
        let result1 = db.insert_cli_version(cli_version_json.clone());
        assert!(result1.is_ok());
        
        // Insert second time - should fail
        let result2 = db.insert_cli_version(cli_version_json);
        assert!(result2.is_err());
        assert!(result2.unwrap_err().to_string().contains("already exists"));
    }

    #[glue::test]
    fn test_insert_many_cli_version() {
        let mut db = VCloudDB::new();
        let cli_versions = vec![
            create_test_cli_version("1.0.0"),
            create_test_cli_version("1.1.0"),
            create_test_cli_version("1.2.0"),
        ];
        let cli_versions_json = serde_json::to_string(&cli_versions).unwrap();
        
        let result = db.insert_many_cli_version(cli_versions_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 3);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify all CLI versions were inserted
        assert!(db.cli_versions.contains(&"1.0.0".to_string()));
        assert!(db.cli_versions.contains(&"1.1.0".to_string()));
        assert!(db.cli_versions.contains(&"1.2.0".to_string()));
    }

    #[glue::test]
    fn test_get_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version
        db.insert_cli_version(cli_version_json).unwrap();
        
        // Get CLI version
        let result = db.get_cli_version("1.0.0".to_string());
        assert!(result.is_ok());
        
        let retrieved_cli_version: CliVersion = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_cli_version.version, "1.0.0");
        assert_eq!(retrieved_cli_version.change_log, "Initial release");
    }

    #[glue::test]
    fn test_get_cli_version_not_found() {
        let db = VCloudDB::new();
        let result = db.get_cli_version("nonexistent".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }

    #[glue::test]
    fn test_find_cli_version() {
        let mut db = VCloudDB::new();
        
        // Insert test CLI versions
        let cli_versions = vec![
            create_test_cli_version("1.0.0"),
            create_test_cli_version("1.1.0"),
            create_test_cli_version("2.0.0"),
        ];
        
        for cli_version in cli_versions {
            let cli_version_json = serde_json::to_string(&cli_version).unwrap();
            db.insert_cli_version(cli_version_json).unwrap();
        }
        
        // Test find with empty filter
        let filter = CliVersionFilter {
            version: None,
            versions: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_cli_version(filter_json);
        assert!(result.is_ok());
        
        let found_cli_versions: Vec<CliVersion> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_cli_versions.len(), 3);
    }

    #[glue::test]
    fn test_find_cli_version_by_version() {
        let mut db = VCloudDB::new();
        
        // Insert test CLI versions
        let cli_versions = vec![
            create_test_cli_version("1.0.0"),
            create_test_cli_version("1.1.0"),
        ];
        
        for cli_version in cli_versions {
            let cli_version_json = serde_json::to_string(&cli_version).unwrap();
            db.insert_cli_version(cli_version_json).unwrap();
        }
        
        // Test find by specific version
        let filter = CliVersionFilter {
            version: Some("1.0.0".to_string()),
            versions: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_cli_version(filter_json);
        assert!(result.is_ok());
        
        let found_cli_versions: Vec<CliVersion> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_cli_versions.len(), 1);
        assert_eq!(found_cli_versions[0].version, "1.0.0");
    }

    #[glue::test]
    fn test_count_cli_version() {
        let mut db = VCloudDB::new();
        
        // Insert test CLI versions
        let cli_versions = vec![
            create_test_cli_version("1.0.0"),
            create_test_cli_version("1.1.0"),
            create_test_cli_version("2.0.0"),
        ];
        
        for cli_version in cli_versions {
            let cli_version_json = serde_json::to_string(&cli_version).unwrap();
            db.insert_cli_version(cli_version_json).unwrap();
        }
        
        // Test count with empty filter
        let filter = CliVersionFilter {
            version: None,
            versions: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.count_cli_version(filter_json);
        assert!(result.is_ok());
        
        let count_result: serde_json::Value = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(count_result["count"], 3);
    }

    #[glue::test]
    fn test_update_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version
        db.insert_cli_version(cli_version_json).unwrap();
        
        // Update CLI version
        let filter = CliVersionFilter {
            version: Some("1.0.0".to_string()),
            versions: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let mut update_data = serde_json::Map::new();
        update_data.insert("changeLog".to_string(), serde_json::Value::String("Updated release".to_string()));
        
        let update_params = CliVersionUpdate {
            filter,
            update_data: serde_json::Value::Object(update_data),
        };
        let update_json = serde_json::to_string(&update_params).unwrap();
        
        let result = db.update_cli_version(update_json);
        assert!(result.is_ok());
        
        // Verify update
        let updated_cli_version = db.cli_versions.get(&"1.0.0".to_string()).unwrap();
        assert_eq!(updated_cli_version.change_log, "Updated release");
    }

    #[glue::test]
    fn test_delete_cli_version() {
        let mut db = VCloudDB::new();
        let cli_version = create_test_cli_version("1.0.0");
        let cli_version_json = serde_json::to_string(&cli_version).unwrap();
        
        // Insert CLI version
        db.insert_cli_version(cli_version_json).unwrap();
        assert!(db.cli_versions.contains(&"1.0.0".to_string()));
        
        // Delete CLI version
        let filter = CliVersionFilter {
            version: Some("1.0.0".to_string()),
            versions: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_cli_version(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion
        assert!(!db.cli_versions.contains(&"1.0.0".to_string()));
    }

    #[glue::test]
    fn test_delete_many_cli_version() {
        let mut db = VCloudDB::new();
        
        // Insert test CLI versions
        let cli_versions = vec![
            create_test_cli_version("1.0.0"),
            create_test_cli_version("1.1.0"),
            create_test_cli_version("2.0.0"),
        ];
        
        for cli_version in cli_versions {
            let cli_version_json = serde_json::to_string(&cli_version).unwrap();
            db.insert_cli_version(cli_version_json).unwrap();
        }
        
        // Delete multiple CLI versions
        let filter = CliVersionFilter {
            version: None,
            versions: Some(vec!["1.0.0".to_string(), "1.1.0".to_string()]),
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_many_cli_version(filter_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify deletions
        assert!(!db.cli_versions.contains(&"1.0.0".to_string()));
        assert!(!db.cli_versions.contains(&"1.1.0".to_string()));
        assert!(db.cli_versions.contains(&"2.0.0".to_string()));
    }
}
