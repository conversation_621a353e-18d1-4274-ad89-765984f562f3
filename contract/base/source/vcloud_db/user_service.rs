// User service module implementation

impl VCloudDB {
    /// Create a new user service from JSON string
    pub fn insert_user_service(&mut self, service_json: String) -> anyhow::Result<String> {
        let mut service: UserService = serde_json::from_str(&service_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;
        // Validate required fields
        if service._id.is_empty() {
            return Err(anyhow::anyhow!("Service ID cannot be empty"));
        }

        if self.user_services.contains(&service._id) {
            return Err(anyhow::anyhow!("User service with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_timestamp_handling(&mut service);

        // Ensure deleted_at is 0 for new services
        service.deleted_at = 0;

        self.user_services.insert(&service._id, &service);
        Ok(service._id)
    }

    /// Create multiple user services in a single transaction
    ///
    /// This function enables efficient batch creation of UserService records with:
    /// - Reduced gas costs through single transaction processing
    /// - Graceful handling of partial failures with detailed error reporting
    /// - Validation of each service object before processing
    /// - Duplicate ID detection both within batch and against existing services
    /// - Atomic transaction behavior for critical operations
    ///
    /// # Parameters
    /// * `services_json` - JSON string containing array of UserService objects
    ///   Example: `[{"id": "service1", "amount": 100.0, ...}, {"id": "service2", "amount": 200.0, ...}]`
    ///
    /// # Returns
    /// JSON string containing BatchResult with:
    /// - `created`: Number of successfully created services
    /// - `updated`: Number of updated services (always 0 for create operations)
    /// - `deleted`: Number of deleted services (always 0 for create operations)
    /// - `errors`: Array of error messages for failed service creations
    ///
    /// # Error Handling
    /// - JSON parsing errors return descriptive error messages
    /// - Individual service validation failures are collected and reported
    /// - Duplicate ID conflicts are handled gracefully without stopping the batch
    /// - Critical errors may cause entire batch rollback due to atomic transaction
    pub fn insert_many_user_service(&mut self, services_json: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let services: Vec<UserService> = serde_json::from_str(&services_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse services JSON: {}. Expected format: [{{\"id\": \"service1\", \"amount\": 100.0, ...}}, ...]", e))?;

        // Validate input is not empty
        if services.is_empty() {
            return Err(anyhow::anyhow!("Services array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut service in services {
            // Validate required fields
            if service._id.is_empty() {
                result.errors.push("Service ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicate IDs within the batch
            if !batch_ids.insert(service._id.clone()) {
                result.errors.push(format!("Duplicate service ID '{}' found within batch", service._id));
                continue;
            }

            // Check for existing service with same ID
            if self.user_services.contains(&service._id) {
                result.errors.push(format!("Service with ID '{}' already exists in database", service._id));
                continue;
            }

            // Validate business logic constraints
            if service.amount < 0.0 {
                result.errors.push(format!("Service '{}' has invalid negative amount: {}", service._id, service.amount));
                continue;
            }

            if service.duration < 0 {
                result.errors.push(format!("Service '{}' has invalid negative duration: {}", service._id, service.duration));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_timestamp_handling(&mut service);

            // Ensure deleted_at is 0 for new services
            service.deleted_at = 0;

            // Insert the service
            self.user_services.insert(&service._id, &service);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single user service by ID
    pub fn get_user_service(&self, id: String) -> anyhow::Result<String> {
        let service = self.user_services.get(&id);
        match service {
            Some(service) => Ok(serde_json::to_string(&service)?),
            None => Err(anyhow::anyhow!("User service not found")),
        }
    }

    /// Update an existing user service from JSON string
    pub fn update_user_service(&mut self, service_json: String) -> anyhow::Result<()> {
        let mut service: UserService = serde_json::from_str(&service_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;

        // Validate required fields
        if service._id.is_empty() {
            return Err(anyhow::anyhow!("Service ID cannot be empty"));
        }

        if !self.user_services.contains(&service._id) {
            return Err(anyhow::anyhow!("User service not found"));
        }

        // Apply timestamp handling logic for updates
        if service.updated_at == 0 {
            service.updated_at = self.get_current_timestamp();
        }

        self.user_services.insert(&service._id, &service);
        Ok(())
    }

    /// Apply timestamp handling logic based on input values
    pub(crate) fn apply_timestamp_handling(&self, service: &mut UserService) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if service.created_at == 0 {
            service.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if service.updated_at == 0 {
            service.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Internal implementation for batch updating user services with partial updates
    pub(crate) fn update_many_user_service(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let update_params: UserServiceUpdate = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceUpdate JSON: {}", e))?;

        let params = update_params.filter;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find services matching the filter criteria
        let mut services_to_update = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(service) = self.user_services.get(id) {
                    if self.matches_user_service_filters(&service, &params) {
                        services_to_update.push(service);
                    }
                }
            }
        } else {
            let mut iter = self.user_services.index("user_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
                if self.matches_user_service_filters(&service, &params) {
                    services_to_update.push(service);
                }
            }
        }

        // Update each matching service with partial data
        for mut service in services_to_update {
            // Parse update data as a generic JSON object for partial updates
            if let Some(update_obj) = update_params.update_data.as_object() {
                // Update only specified fields
                for (key, value) in update_obj {
                    match key.as_str() {
                        "duration" => {
                            if let Some(duration) = value.as_i64() {
                                service.duration = duration;
                            }
                        }
                        "amount" => {
                            if let Some(amount) = value.as_f64() {
                                service.amount = amount;
                            }
                        }
                        "status" => {
                            if let Some(status) = value.as_str() {
                                service.status = status.to_string();
                            }
                        }
                        "serviceActivated" => {
                            if let Some(activated) = value.as_bool() {
                                service.service_activated = activated;
                            }
                        }
                        // Add more fields as needed
                        _ => {} // Ignore unknown fields
                    }
                }
            }

            self.user_services.insert(&service._id, &service);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for bulk write operations on user services
    pub(crate) fn bulk_write_user_service(&mut self, bulk_write_json_string: String) -> anyhow::Result<String> {
        let operations: Vec<UserServiceBulkWriteOperation> = serde_json::from_str(&bulk_write_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceBulkWriteOperation JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<UserService>(data) {
                            Ok(mut service) => {
                                if service._id.is_empty() {
                                    result.errors.push("Service ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.user_services.contains(&service._id) {
                                    result.errors.push(format!("Service with ID '{}' already exists", service._id));
                                    continue;
                                }
                                self.apply_timestamp_handling(&mut service);
                                service.deleted_at = 0;
                                self.user_services.insert(&service._id, &service);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse service data: {}", e));
                            }
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        let update_params = UserServiceUpdate {
                            filter,
                            update_data: data,
                        };
                        let update_json = serde_json::to_string(&update_params)?;
                        match self.update_many_user_service(update_json) {
                            Ok(update_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                    result.updated += batch_result.updated;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Update operation failed: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        let filter_json = serde_json::to_string(&filter)?;
                        match self.delete_many_user_service(filter_json) {
                            Ok(delete_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                                    result.deleted += batch_result.deleted;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Delete operation failed: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single user service (HARD DELETE)
    pub(crate) fn delete_user_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: UserServiceQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.user_services.contains(id) {
                    self.user_services.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching service
        let mut iter = self.user_services.index("user_services_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
            if self.matches_user_service_filters(&service, &params) {
                // Hard delete: completely remove from storage
                self.user_services.remove(&service._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting user services
    pub(crate) fn delete_many_user_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: UserServiceQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find services matching the filter criteria
        let mut services_to_delete = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(service) = self.user_services.get(id) {
                    if self.matches_user_service_filters(&service, &params) {
                        services_to_delete.push(service);
                    }
                }
            }
        } else {
            let mut iter = self.user_services.index("user_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
                if self.matches_user_service_filters(&service, &params) {
                    services_to_delete.push(service);
                }
            }
        }

        // Delete each matching service (HARD DELETE)
        for service in services_to_delete {
            // Hard delete: completely remove from storage
            self.user_services.remove(&service._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query user services with comprehensive filtering, pagination, and sorting
    pub fn find_user_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: UserServiceQueryParams = serde_json::from_str(&params_json)?;

        let mut services = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch services by IDs directly
            for id in ids {
                if let Some(service) = self.user_services.get(id) {
                    if self.matches_user_service_filters(&service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if services.len() >= limit as usize {
                            break;
                        }
                        services.push(service);
                        count += 1;
                    }
                }
            }
        } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
            // Use composite address_status index with efficient sorting
            let key_prefix = format!("{}-{}", address, status);
            self.query_user_service_with_index("user_services_address_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
        } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
            // Use composite address_service_activated index with efficient sorting
            let key_prefix = format!("{}-{}", address, service_activated);
            self.query_user_service_with_index("user_services_address_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
        } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
            // Use composite provider_status index with efficient sorting
            let key_prefix = format!("{}-{}", provider, status);
            self.query_user_service_with_index("user_services_provider_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(ref address) = params.address {
            // Use address index with efficient sorting
            self.query_user_service_with_index("user_services_address", address, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(ref provider) = params.provider {
            // Use provider index with efficient sorting
            self.query_user_service_with_index("user_services_provider", provider, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(ref provider_address) = params.provider_address {
            // Use provider_address index with efficient sorting
            self.query_user_service_with_index("user_services_provider_address", provider_address, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(ref status) = params.status {
            // Use status index with efficient sorting
            self.query_user_service_with_index("user_services_status", status, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(service_activated) = params.service_activated {
            // Use service_activated index with efficient sorting
            let key_prefix = service_activated.to_string();
            self.query_user_service_with_index("user_services_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
        } else if let Some(ref service_id) = params.service_id {
            // Use service_id index with efficient sorting
            self.query_user_service_with_index("user_services_service_id", service_id, &params, &mut services, &mut count, limit, offset)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.query_user_service_with_created_at(&params, &mut services, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&services)?)
    }

    /// Count user services with advanced filtering
    pub fn count_user_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: UserServiceQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch services by IDs directly
            for id in ids {
                if let Some(service) = self.user_services.get(id) {
                    if self.matches_user_service_filters(&service, &params) {
                        count += 1;
                    }
                }
            }
        } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
            // Use composite address_status index with efficient sorting
            let key_prefix = format!("{}-{}", address, status);
            self.count_user_service_with_index("user_services_address_status", &key_prefix, &params, &mut count)?;
        } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
            // Use composite address_service_activated index with efficient sorting
            let key_prefix = format!("{}-{}", address, service_activated);
            self.count_user_service_with_index("user_services_address_service_activated", &key_prefix, &params, &mut count)?;
        } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
            // Use composite provider_status index with efficient sorting
            let key_prefix = format!("{}-{}", provider, status);
            self.count_user_service_with_index("user_services_provider_status", &key_prefix, &params, &mut count)?;
        } else if let Some(ref address) = params.address {
            // Use address index with efficient sorting
            self.count_user_service_with_index("user_services_address", address, &params, &mut count)?;
        } else if let Some(ref provider) = params.provider {
            // Use provider index with efficient sorting
            self.count_user_service_with_index("user_services_provider", provider, &params, &mut count)?;
        } else if let Some(ref provider_address) = params.provider_address {
            // Use provider_address index with efficient sorting
            self.count_user_service_with_index("user_services_provider_address", provider_address, &params, &mut count)?;
        } else if let Some(ref status) = params.status {
            // Use status index with efficient sorting
            self.count_user_service_with_index("user_services_status", status, &params, &mut count)?;
        } else if let Some(service_activated) = params.service_activated {
            // Use service_activated index with efficient sorting
            let key_prefix = service_activated.to_string();
            self.count_user_service_with_index("user_services_service_activated", &key_prefix, &params, &mut count)?;
        } else if let Some(ref service_id) = params.service_id {
            // Use service_id index with efficient sorting
            self.count_user_service_with_index("user_services_service_id", service_id, &params, &mut count)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.count_user_service_with_created_at(&params, &mut count)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(count.to_string())
    }

    /// Helper function to check if a service matches the given filters
    pub(crate) fn matches_user_service_filters(&self, service: &UserService, params: &UserServiceQueryParams) -> bool {
        // Skip deleted services unless specifically querying for them
        if service.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&service._id) {
                return false;
            }
        }

        // Check service_id filter
        if let Some(ref service_id) = params.service_id {
            if service.service_id != *service_id {
                return false;
            }
        }

        // Check address filter
        if let Some(ref address) = params.address {
            if service.address != *address {
                return false;
            }
        }

        // Check provider filter
        if let Some(ref provider) = params.provider {
            if service.provider != *provider {
                return false;
            }
        }

        // Check provider_address filter
        if let Some(ref provider_address) = params.provider_address {
            if service.provider_address != *provider_address {
                return false;
            }
        }

        // Check status filter
        if let Some(ref status) = params.status {
            if service.status != *status {
                return false;
            }
        }

        // Check service_activated filter
        if let Some(service_activated) = params.service_activated {
            if service.service_activated != service_activated {
                return false;
            }
        }

        // Check created_at time range
        if let Some(start) = params.created_at_start {
            if service.created_at < start {
                return false;
            }
        }
        if let Some(end) = params.created_at_end {
            if service.created_at > end {
                return false;
            }
        }

        // Check updated_at time range
        if let Some(start) = params.updated_at_start {
            if service.updated_at < start {
                return false;
            }
        }
        if let Some(end) = params.updated_at_end {
            if service.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_user_service_with_created_at(
        &self,
        params: &UserServiceQueryParams,
        services: &mut Vec<UserService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.user_services.index("user_services_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let service = iter.value()?;
            if self.matches_user_service_filters(&service, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if services.len() >= limit as usize {
                    break;
                }
                services.push(service);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_user_service_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &UserServiceQueryParams,
        services: &mut Vec<UserService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first
            (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
        } else {
            // Ascending: oldest first
            (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
        };
        let mut iter = self.user_services.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let service = iter.value()?;
            if self.matches_user_service_filters(&service, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if services.len() >= limit as usize {
                    break;
                }
                services.push(service);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_user_service_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &UserServiceQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.user_services.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let service = iter.value()?;
            if self.matches_user_service_filters(&service, &params) {
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_user_service_with_created_at(
        &self,
        params: &UserServiceQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        let mut iter = self.user_services.index("user_services_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let service = iter.value()?;
            if self.matches_user_service_filters(&service, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}


