// Currency module implementation

impl VCloudDB {
    /// Create a new currency from JSON string
    pub fn insert_currency(&mut self, currency_json: String) -> anyhow::Result<String> {
        let mut currency: Currency = serde_json::from_str(&currency_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse currency JSON: {}", e))?;

        // Validate required fields
        if currency._id.is_empty() {
            return Err(anyhow::anyhow!("Currency ID cannot be empty"));
        }

        if self.currencies.contains(&currency._id) {
            return Err(anyhow::anyhow!("Currency with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_currency_timestamp_handling(&mut currency);

        // Ensure deleted_at is 0 for new currencies
        currency.deleted_at = 0;

        self.currencies.insert(&currency._id, &currency);
        Ok(currency._id)
    }

    /// Internal implementation for batch inserting currencies
    pub(crate) fn insert_many_currency(&mut self, insert_many_json_string: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let currencies: Vec<Currency> = serde_json::from_str(&insert_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse currencies JSON: {}. Expected format: [{{\"_id\": \"currency1\", \"nameOrId\": \"BTC\", ...}}, ...]", e))?;

        // Validate input is not empty
        if currencies.is_empty() {
            return Err(anyhow::anyhow!("Currencies array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut currency in currencies {
            // Validate required fields
            if currency._id.is_empty() {
                result.errors.push("Currency ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicates within the batch
            if batch_ids.contains(&currency._id) {
                result.errors.push(format!("Duplicate currency ID in batch: {}", currency._id));
                continue;
            }
            batch_ids.insert(currency._id.clone());

            // Check if currency already exists in database
            if self.currencies.contains(&currency._id) {
                result.errors.push(format!("Currency '{}' already exists", currency._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_currency_timestamp_handling(&mut currency);

            // Ensure deleted_at is 0 for new currencies
            currency.deleted_at = 0;

            // Insert the currency
            self.currencies.insert(&currency._id, &currency);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single currency by ID
    pub fn get_currency(&self, id: String) -> anyhow::Result<String> {
        let currency = self.currencies.get(&id);
        match currency {
            Some(currency) => Ok(serde_json::to_string(&currency)?),
            None => Err(anyhow::anyhow!("Currency not found")),
        }
    }

    /// Apply timestamp handling logic for currencies
    pub(crate) fn apply_currency_timestamp_handling(&self, currency: &mut Currency) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if currency.created_at == 0 {
            currency.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if currency.updated_at == 0 {
            currency.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update currencies with advanced filtering
    pub fn update_currency(&mut self, update_json_string: String) -> anyhow::Result<String> {
        let update_params: CurrencyUpdate = serde_json::from_str(&update_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyUpdate JSON: {}", e))?;

        let mut updated_count = 0u64;

        // Find currencies to update
        let currencies_to_update = self.find_currencies_for_update(&update_params.filter)?;

        for mut currency in currencies_to_update {
            // Apply updates from update_data
            if let Some(name_or_id) = update_params.update_data.get("nameOrId").and_then(|v| v.as_str()) {
                currency.name_or_id = name_or_id.to_string();
            }
            if let Some(contract_id) = update_params.update_data.get("contractId").and_then(|v| v.as_str()) {
                currency.contract_id = contract_id.to_string();
            }
            if let Some(symbol_name) = update_params.update_data.get("symbolName").and_then(|v| v.as_str()) {
                currency.symbol_name = symbol_name.to_string();
            }
            if let Some(contract_type) = update_params.update_data.get("contractType").and_then(|v| v.as_str()) {
                currency.contract_type = contract_type.to_string();
            }
            if let Some(unit) = update_params.update_data.get("unit").and_then(|v| v.as_i64()) {
                currency.unit = unit as i32;
            }
            if let Some(exchange_rate) = update_params.update_data.get("exchangeRate").and_then(|v| v.as_f64()) {
                currency.exchange_rate = exchange_rate;
            }

            // Always update the updated_at timestamp
            currency.updated_at = self.get_current_timestamp();

            // Update in storage
            self.currencies.insert(&currency._id, &currency);
            updated_count += 1;
        }

        Ok(format!("{{\"updated\": {}}}", updated_count))
    }

    /// Update many currencies with advanced filtering
    pub fn update_many_currency(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let updates: Vec<CurrencyUpdate> = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse currency updates JSON: {}", e))?;

        let mut total_updated = 0u64;
        let mut errors = Vec::new();

        for update in updates {
            match self.update_currency(serde_json::to_string(&update)?) {
                Ok(result_str) => {
                    if let Ok(result) = serde_json::from_str::<serde_json::Value>(&result_str) {
                        if let Some(updated) = result.get("updated").and_then(|v| v.as_u64()) {
                            total_updated += updated;
                        }
                    }
                }
                Err(e) => {
                    errors.push(format!("Update failed: {}", e));
                }
            }
        }

        let result = BatchResult {
            created: 0,
            updated: total_updated,
            deleted: 0,
            errors,
        };

        Ok(serde_json::to_string(&result)?)
    }

    /// Helper function to find currencies for update operations
    fn find_currencies_for_update(&self, filter: &CurrencyFilter) -> anyhow::Result<Vec<Currency>> {
        let mut currencies = Vec::new();

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = filter.ids {
            for id in ids {
                if let Some(currency) = self.currencies.get(id) {
                    if self.matches_currency_filters(&currency, filter) {
                        currencies.push(currency);
                    }
                }
            }
        } else if let Some(ref name_or_id) = filter.name_or_id {
            // Use name_or_id index
            self.query_currency_with_index("currencies_name_or_id", name_or_id, filter, &mut currencies)?;
        } else if let Some(ref symbol_name) = filter.symbol_name {
            // Use symbol_name index
            self.query_currency_with_index("currencies_symbol_name", symbol_name, filter, &mut currencies)?;
        } else if let Some(ref contract_id) = filter.contract_id {
            // Use contract_id index
            self.query_currency_with_index("currencies_contract_id", contract_id, filter, &mut currencies)?;
        } else {
            // Use created_at index for general queries
            let mut iter = self.currencies.index("currencies_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while let Some((_, currency)) = iter.next() {
                if self.matches_currency_filters(&currency, filter) {
                    currencies.push(currency);
                }
            }
        }

        Ok(currencies)
    }

    /// Helper function to query currencies with a specific index
    fn query_currency_with_index(
        &self,
        index_name: &str,
        key: &str,
        filter: &CurrencyFilter,
        currencies: &mut Vec<Currency>,
    ) -> anyhow::Result<()> {
        let key_prefix = format!("{}-", key);
        let mut iter = self.currencies.index(index_name).iter(
            false,
            &key_prefix,
            &format!("{}~", key_prefix)
        );

        while let Some((_, currency)) = iter.next() {
            if self.matches_currency_filters(&currency, filter) {
                currencies.push(currency);
            }
        }

        Ok(())
    }

    /// Internal implementation for deleting a single currency (HARD DELETE)
    pub(crate) fn delete_currency(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CurrencyFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyFilter JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.currencies.contains(id) {
                    // Hard delete: completely remove from storage
                    self.currencies.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching currency
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while let Some((_, currency)) = iter.next() {
            if self.matches_currency_filters(&currency, &params) {
                // Hard delete: completely remove from storage
                self.currencies.remove(&currency._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Delete many currencies with advanced filtering (HARD DELETE)
    pub fn delete_many_currency(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CurrencyFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyFilter JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Collect currencies to delete
        let mut currencies_to_delete = Vec::new();

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(currency) = self.currencies.get(id) {
                    if self.matches_currency_filters(&currency, &params) {
                        currencies_to_delete.push(currency);
                    }
                }
            }
        } else if let Some(ref name_or_id) = params.name_or_id {
            // Use name_or_id index
            self.query_currency_with_index("currencies_name_or_id", name_or_id, &params, &mut currencies_to_delete)?;
        } else if let Some(ref symbol_name) = params.symbol_name {
            // Use symbol_name index
            self.query_currency_with_index("currencies_symbol_name", symbol_name, &params, &mut currencies_to_delete)?;
        } else if let Some(ref contract_id) = params.contract_id {
            // Use contract_id index
            self.query_currency_with_index("currencies_contract_id", contract_id, &params, &mut currencies_to_delete)?;
        } else {
            // Use created_at index for general queries
            let mut iter = self.currencies.index("currencies_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while let Some((_, currency)) = iter.next() {
                if self.matches_currency_filters(&currency, &params) {
                    currencies_to_delete.push(currency);
                }
            }
        }

        // Delete each matching currency (HARD DELETE)
        for currency in currencies_to_delete {
            // Hard delete: completely remove from storage
            self.currencies.remove(&currency._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query currencies with comprehensive filtering, pagination, and sorting
    pub fn find_currency(&self, params_json: String) -> anyhow::Result<String> {
        let params: CurrencyFilter = serde_json::from_str(&params_json)?;

        let mut currencies = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(currency) = self.currencies.get(id) {
                    if self.matches_currency_filters(&currency, &params) {
                        if count >= offset && currencies.len() < limit as usize {
                            currencies.push(currency);
                        }
                        count += 1;
                    }
                }
            }
        } else if let Some(ref name_or_id) = params.name_or_id {
            // Use name_or_id index with efficient sorting
            self.query_currency_with_index_paginated("currencies_name_or_id", name_or_id, &params, &mut currencies, &mut count, limit, offset)?;
        } else if let Some(ref symbol_name) = params.symbol_name {
            // Use symbol_name index with efficient sorting
            self.query_currency_with_index_paginated("currencies_symbol_name", symbol_name, &params, &mut currencies, &mut count, limit, offset)?;
        } else if let Some(ref contract_id) = params.contract_id {
            // Use contract_id index with efficient sorting
            self.query_currency_with_index_paginated("currencies_contract_id", contract_id, &params, &mut currencies, &mut count, limit, offset)?;
        } else {
            // Use created_at index with efficient sorting
            self.query_currency_with_created_at(&params, &mut currencies, &mut count, limit, offset)?;
        }

        Ok(serde_json::to_string(&currencies)?)
    }

    /// Helper function to check if a currency matches the given filters
    pub(crate) fn matches_currency_filters(&self, currency: &Currency, params: &CurrencyFilter) -> bool {
        // Skip deleted currencies unless specifically querying for them
        if currency.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&currency._id) {
                return false;
            }
        }

        // Check name_or_id filter
        if let Some(ref name_or_id) = params.name_or_id {
            if currency.name_or_id != *name_or_id {
                return false;
            }
        }

        // Check contract_id filter
        if let Some(ref contract_id) = params.contract_id {
            if currency.contract_id != *contract_id {
                return false;
            }
        }

        // Check symbol_name filter
        if let Some(ref symbol_name) = params.symbol_name {
            if currency.symbol_name != *symbol_name {
                return false;
            }
        }

        // Check contract_type filter
        if let Some(ref contract_type) = params.contract_type {
            if currency.contract_type != *contract_type {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if currency.created_at < start {
                return false;
            }
        }

        if let Some(end) = params.created_at_end {
            if currency.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if currency.updated_at < start {
                return false;
            }
        }

        if let Some(end) = params.updated_at_end {
            if currency.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Helper function to query currencies using created_at index with efficient sorting
    fn query_currency_with_created_at(
        &self,
        params: &CurrencyFilter,
        currencies: &mut Vec<Currency>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        let sort_desc = params.sort_desc.unwrap_or(true);

        let mut iter = self.currencies.index("currencies_created_at").iter(
            sort_desc,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while let Some((_, currency)) = iter.next() {
            if self.matches_currency_filters(&currency, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if currencies.len() >= limit as usize {
                    break;
                }
                currencies.push(currency);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Helper function to query currencies with a specific index and pagination
    fn query_currency_with_index_paginated(
        &self,
        index_name: &str,
        key: &str,
        params: &CurrencyFilter,
        currencies: &mut Vec<Currency>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        let sort_desc = params.sort_desc.unwrap_or(true);
        let key_prefix = format!("{}-", key);

        let mut iter = self.currencies.index(index_name).iter(
            sort_desc,
            &key_prefix,
            &format!("{}~", key_prefix)
        );

        while let Some((_, currency)) = iter.next() {
            if self.matches_currency_filters(&currency, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if currencies.len() >= limit as usize {
                    break;
                }
                currencies.push(currency);
                *count += 1;
            }
        }

        Ok(())
    }
}
