// CLI Version module implementation

impl VCloudDB {
    /// Create a new CLI version from JSON string
    pub fn insert_cli_version(&mut self, cli_version_json: String) -> anyhow::Result<String> {
        let mut cli_version: CliVersion = serde_json::from_str(&cli_version_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI version JSON: {}", e))?;

        // Validate required fields
        if cli_version.version.is_empty() {
            return Err(anyhow::anyhow!("CLI version cannot be empty"));
        }

        if self.cli_versions.contains(&cli_version.version) {
            return Err(anyhow::anyhow!("CLI version with this version already exists"));
        }

        // Apply timestamp handling logic
        self.apply_cli_version_timestamp_handling(&mut cli_version);

        // Ensure deleted_at is 0 for new CLI versions
        cli_version.deleted_at = 0;

        self.cli_versions.insert(&cli_version.version, &cli_version);
        Ok(cli_version.version)
    }

    /// Internal implementation for batch inserting CLI versions
    pub(crate) fn insert_many_cli_version(&mut self, insert_many_json_string: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let cli_versions: Vec<CliVersion> = serde_json::from_str(&insert_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI versions JSON: {}. Expected format: [{{\"version\": \"1.0.0\", \"changeLog\": \"...\", ...}}, ...]", e))?;

        // Validate input is not empty
        if cli_versions.is_empty() {
            return Err(anyhow::anyhow!("CLI versions array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track versions within this batch to detect duplicates
        let mut batch_versions = std::collections::HashSet::new();

        for mut cli_version in cli_versions {
            // Validate required fields
            if cli_version.version.is_empty() {
                result.errors.push("CLI version cannot be empty".to_string());
                continue;
            }

            // Check for duplicates within the batch
            if batch_versions.contains(&cli_version.version) {
                result.errors.push(format!("Duplicate CLI version in batch: {}", cli_version.version));
                continue;
            }
            batch_versions.insert(cli_version.version.clone());

            // Check if CLI version already exists in database
            if self.cli_versions.contains(&cli_version.version) {
                result.errors.push(format!("CLI version '{}' already exists", cli_version.version));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_cli_version_timestamp_handling(&mut cli_version);

            // Ensure deleted_at is 0 for new CLI versions
            cli_version.deleted_at = 0;

            // Insert the CLI version
            self.cli_versions.insert(&cli_version.version, &cli_version);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single CLI version by version
    pub fn get_cli_version(&self, version: String) -> anyhow::Result<String> {
        let cli_version = self.cli_versions.get(&version);
        match cli_version {
            Some(cli_version) => Ok(serde_json::to_string(&cli_version)?),
            None => Err(anyhow::anyhow!("CLI version not found")),
        }
    }

    /// Apply timestamp handling logic for CLI versions
    pub(crate) fn apply_cli_version_timestamp_handling(&self, cli_version: &mut CliVersion) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if cli_version.created_at == 0 {
            cli_version.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if cli_version.updated_at == 0 {
            cli_version.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update CLI versions with advanced filtering
    pub fn update_cli_version(&mut self, update_json_string: String) -> anyhow::Result<String> {
        let update_params: CliVersionUpdate = serde_json::from_str(&update_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionUpdate JSON: {}", e))?;

        let mut updated_count = 0u64;

        // Find CLI versions to update
        let cli_versions_to_update = self.find_cli_versions_for_update(&update_params.filter)?;

        for mut cli_version in cli_versions_to_update {
            // Apply updates from update_data
            if let Some(change_log) = update_params.update_data.get("changeLog").and_then(|v| v.as_str()) {
                cli_version.change_log = change_log.to_string();
            }
            if let Some(minimal_supported) = update_params.update_data.get("minimalSupported").and_then(|v| v.as_str()) {
                cli_version.minimal_supported = minimal_supported.to_string();
            }

            // Always update the updated_at timestamp
            cli_version.updated_at = self.get_current_timestamp();

            // Update in storage
            self.cli_versions.insert(&cli_version.version, &cli_version);
            updated_count += 1;
        }

        Ok(format!("{{\"updated\": {}}}", updated_count))
    }

    /// Update many CLI versions with advanced filtering
    pub fn update_many_cli_version(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let updates: Vec<CliVersionUpdate> = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CLI version updates JSON: {}", e))?;

        let mut total_updated = 0u64;
        let mut errors = Vec::new();

        for update in updates {
            match self.update_cli_version(serde_json::to_string(&update)?) {
                Ok(result_str) => {
                    if let Ok(result) = serde_json::from_str::<serde_json::Value>(&result_str) {
                        if let Some(updated) = result.get("updated").and_then(|v| v.as_u64()) {
                            total_updated += updated;
                        }
                    }
                }
                Err(e) => {
                    errors.push(format!("Update failed: {}", e));
                }
            }
        }

        let result = BatchResult {
            created: 0,
            updated: total_updated,
            deleted: 0,
            errors,
        };

        Ok(serde_json::to_string(&result)?)
    }

    /// Helper function to find CLI versions for update operations
    fn find_cli_versions_for_update(&self, filter: &CliVersionFilter) -> anyhow::Result<Vec<CliVersion>> {
        let mut cli_versions = Vec::new();

        // Use version filter if provided
        if let Some(ref version) = filter.version {
            if let Some(cli_version) = self.cli_versions.get(version) {
                if self.matches_cli_version_filters(&cli_version, filter) {
                    cli_versions.push(cli_version);
                }
            }
        } else if let Some(ref versions) = filter.versions {
            // Use versions filter for batch queries
            for version in versions {
                if let Some(cli_version) = self.cli_versions.get(version) {
                    if self.matches_cli_version_filters(&cli_version, filter) {
                        cli_versions.push(cli_version);
                    }
                }
            }
        } else {
            // Use created_at index for general queries
            let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while iter.next() {
                let cli_version = iter.value()?;
                if self.matches_cli_version_filters(&cli_version, filter) {
                    cli_versions.push(cli_version);
                }
            }
        }

        Ok(cli_versions)
    }

    /// Internal implementation for deleting a single CLI version (HARD DELETE)
    pub(crate) fn delete_cli_version(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CliVersionFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionFilter JSON: {}", e))?;

        // If version is provided, delete it directly
        if let Some(ref version) = params.version {
            if self.cli_versions.contains(version) {
                // Hard delete: completely remove from storage
                self.cli_versions.remove(version);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        // If versions are provided, delete the first matching one
        if let Some(ref versions) = params.versions {
            if let Some(version) = versions.first() {
                if self.cli_versions.contains(version) {
                    self.cli_versions.remove(version);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching CLI version
        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, &params) {
                // Hard delete: completely remove from storage
                self.cli_versions.remove(&cli_version.version);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Delete many CLI versions with advanced filtering (HARD DELETE)
    pub fn delete_many_cli_version(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CliVersionFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CliVersionFilter JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Collect CLI versions to delete
        let mut cli_versions_to_delete = Vec::new();

        // Use version filter if provided
        if let Some(ref version) = params.version {
            if let Some(cli_version) = self.cli_versions.get(version) {
                if self.matches_cli_version_filters(&cli_version, &params) {
                    cli_versions_to_delete.push(cli_version);
                }
            }
        } else if let Some(ref versions) = params.versions {
            // Use versions filter for batch queries
            for version in versions {
                if let Some(cli_version) = self.cli_versions.get(version) {
                    if self.matches_cli_version_filters(&cli_version, &params) {
                        cli_versions_to_delete.push(cli_version);
                    }
                }
            }
        } else {
            // Use created_at index for general queries
            let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while iter.next() {
                let cli_version = iter.value()?;
                if self.matches_cli_version_filters(&cli_version, &params) {
                    cli_versions_to_delete.push(cli_version);
                }
            }
        }

        // Delete each matching CLI version (HARD DELETE)
        for cli_version in cli_versions_to_delete {
            // Hard delete: completely remove from storage
            self.cli_versions.remove(&cli_version.version);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query CLI versions with comprehensive filtering, pagination, and sorting
    pub fn find_cli_version(&self, params_json: String) -> anyhow::Result<String> {
        let params: CliVersionFilter = serde_json::from_str(&params_json)?;

        let mut cli_versions = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use direct version lookup if version is provided
        if let Some(ref version) = params.version {
            if let Some(cli_version) = self.cli_versions.get(version) {
                if self.matches_cli_version_filters(&cli_version, &params) {
                    if count >= offset && cli_versions.len() < limit as usize {
                        cli_versions.push(cli_version);
                    }
                    count += 1;
                }
            }
        } else if let Some(ref versions) = params.versions {
            // Use versions filter for batch queries
            for version in versions {
                if let Some(cli_version) = self.cli_versions.get(version) {
                    if self.matches_cli_version_filters(&cli_version, &params) {
                        if count >= offset && cli_versions.len() < limit as usize {
                            cli_versions.push(cli_version);
                        }
                        count += 1;
                    }
                }
            }
        } else {
            // Use created_at index with efficient sorting
            self.query_cli_version_with_created_at(&params, &mut cli_versions, &mut count, limit, offset)?;
        }

        Ok(serde_json::to_string(&cli_versions)?)
    }

    /// Count CLI versions with advanced filtering
    pub fn count_cli_version(&self, params_json: String) -> anyhow::Result<String> {
        let params: CliVersionFilter = serde_json::from_str(&params_json)?;
        let mut count = 0u64;

        // Use direct version lookup if version is provided
        if let Some(ref version) = params.version {
            if let Some(cli_version) = self.cli_versions.get(version) {
                if self.matches_cli_version_filters(&cli_version, &params) {
                    count += 1;
                }
            }
        } else if let Some(ref versions) = params.versions {
            // Use versions filter for batch queries
            for version in versions {
                if let Some(cli_version) = self.cli_versions.get(version) {
                    if self.matches_cli_version_filters(&cli_version, &params) {
                        count += 1;
                    }
                }
            }
        } else {
            // Use created_at index for general queries
            let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while iter.next() {
                let cli_version = iter.value()?;
                if self.matches_cli_version_filters(&cli_version, &params) {
                    count += 1;
                }
            }
        }

        Ok(format!("{{\"count\": {}}}", count))
    }

    /// Helper function to check if a CLI version matches the given filters
    pub(crate) fn matches_cli_version_filters(&self, cli_version: &CliVersion, params: &CliVersionFilter) -> bool {
        // Skip deleted CLI versions unless specifically querying for them
        if cli_version.deleted_at > 0 {
            return false;
        }

        // Check version filter
        if let Some(ref version) = params.version {
            if cli_version.version != *version {
                return false;
            }
        }

        // Check versions filter (batch version query)
        if let Some(ref versions) = params.versions {
            if !versions.contains(&cli_version.version) {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if cli_version.created_at < start {
                return false;
            }
        }

        if let Some(end) = params.created_at_end {
            if cli_version.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if cli_version.updated_at < start {
                return false;
            }
        }

        if let Some(end) = params.updated_at_end {
            if cli_version.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Helper function to query CLI versions using created_at index with efficient sorting
    fn query_cli_version_with_created_at(
        &self,
        params: &CliVersionFilter,
        cli_versions: &mut Vec<CliVersion>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        let sort_desc = params.sort_desc.unwrap_or(true);

        let mut iter = self.cli_versions.index("cli_versions_created_at").iter(
            sort_desc,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let cli_version = iter.value()?;
            if self.matches_cli_version_filters(&cli_version, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if cli_versions.len() >= limit as usize {
                    break;
                }
                cli_versions.push(cli_version);
                *count += 1;
            }
        }

        Ok(())
    }
}
