#[cfg(test)]
mod order_service_tests {
    use super::*;

    fn create_test_order_service(id: &str) -> OrderService {
        OrderService {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            order_id: "order_001".to_string(),
            user_service_id: "user_service_001".to_string(),
            order_status: "pending".to_string(),
            order_type: "compute".to_string(),
        }
    }

    #[glue::test]
    fn test_insert_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("os_001");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        let result = db.insert_order_service(order_service_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "os_001");
        
        // Verify order service was inserted
        assert!(db.order_services.contains(&"os_001".to_string()));
    }

    #[glue::test]
    fn test_insert_order_service_duplicate() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("os_001");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert first time - should succeed
        let result1 = db.insert_order_service(order_service_json.clone());
        assert!(result1.is_ok());
        
        // Insert second time - should fail
        let result2 = db.insert_order_service(order_service_json);
        assert!(result2.is_err());
        assert!(result2.unwrap_err().to_string().contains("already exists"));
    }

    #[glue::test]
    fn test_insert_many_order_service() {
        let mut db = VCloudDB::new();
        let order_services = vec![
            create_test_order_service("os_001"),
            {
                let mut os2 = create_test_order_service("os_002");
                os2.order_id = "order_002".to_string();
                os2.user_service_id = "user_service_002".to_string();
                os2
            },
            {
                let mut os3 = create_test_order_service("os_003");
                os3.order_id = "order_003".to_string();
                os3.user_service_id = "user_service_003".to_string();
                os3.order_status = "completed".to_string();
                os3
            },
        ];
        let order_services_json = serde_json::to_string(&order_services).unwrap();
        
        let result = db.insert_many_order_service(order_services_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 3);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify all order services were inserted
        assert!(db.order_services.contains(&"os_001".to_string()));
        assert!(db.order_services.contains(&"os_002".to_string()));
        assert!(db.order_services.contains(&"os_003".to_string()));
    }

    #[glue::test]
    fn test_get_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("os_001");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service
        db.insert_order_service(order_service_json).unwrap();
        
        // Get order service
        let result = db.get_order_service("os_001".to_string());
        assert!(result.is_ok());
        
        let retrieved_order_service: OrderService = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_order_service._id, "os_001");
        assert_eq!(retrieved_order_service.order_id, "order_001");
    }

    #[glue::test]
    fn test_get_order_service_not_found() {
        let db = VCloudDB::new();
        let result = db.get_order_service("nonexistent".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }

    #[glue::test]
    fn test_find_order_service() {
        let mut db = VCloudDB::new();
        
        // Insert test order services
        let order_services = vec![
            create_test_order_service("os_001"),
            {
                let mut os2 = create_test_order_service("os_002");
                os2.order_id = "order_002".to_string();
                os2.user_service_id = "user_service_002".to_string();
                os2
            },
        ];
        
        for order_service in order_services {
            let order_service_json = serde_json::to_string(&order_service).unwrap();
            db.insert_order_service(order_service_json).unwrap();
        }
        
        // Test find with empty filter
        let filter = OrderServiceFilter {
            ids: None,
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_order_service(filter_json);
        assert!(result.is_ok());
        
        let found_order_services: Vec<OrderService> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_order_services.len(), 2);
    }

    #[glue::test]
    fn test_find_order_service_by_user_service_id() {
        let mut db = VCloudDB::new();
        
        // Insert test order services
        let order_services = vec![
            create_test_order_service("os_001"),
            {
                let mut os2 = create_test_order_service("os_002");
                os2.order_id = "order_002".to_string();
                os2.user_service_id = "user_service_002".to_string();
                os2
            },
        ];
        
        for order_service in order_services {
            let order_service_json = serde_json::to_string(&order_service).unwrap();
            db.insert_order_service(order_service_json).unwrap();
        }
        
        // Test find by user service ID
        let filter = OrderServiceFilter {
            ids: None,
            order_id: None,
            user_service_id: Some("user_service_001".to_string()),
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_order_service(filter_json);
        assert!(result.is_ok());
        
        let found_order_services: Vec<OrderService> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_order_services.len(), 1);
        assert_eq!(found_order_services[0].user_service_id, "user_service_001");
    }

    #[glue::test]
    fn test_count_order_service() {
        let mut db = VCloudDB::new();
        
        // Insert test order services
        let order_services = vec![
            create_test_order_service("os_001"),
            {
                let mut os2 = create_test_order_service("os_002");
                os2.order_id = "order_002".to_string();
                os2.user_service_id = "user_service_002".to_string();
                os2
            },
            {
                let mut os3 = create_test_order_service("os_003");
                os3.order_id = "order_003".to_string();
                os3.user_service_id = "user_service_003".to_string();
                os3
            },
        ];
        
        for order_service in order_services {
            let order_service_json = serde_json::to_string(&order_service).unwrap();
            db.insert_order_service(order_service_json).unwrap();
        }
        
        // Test count with empty filter
        let filter = OrderServiceFilter {
            ids: None,
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.count_order_service(filter_json);
        assert!(result.is_ok());
        
        let count_result: serde_json::Value = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(count_result["count"], 3);
    }

    #[glue::test]
    fn test_update_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("os_001");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service
        db.insert_order_service(order_service_json).unwrap();
        
        // Update order service
        let filter = OrderServiceFilter {
            ids: Some(vec!["os_001".to_string()]),
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let mut update_data = serde_json::Map::new();
        update_data.insert("orderStatus".to_string(), serde_json::Value::String("completed".to_string()));
        
        let update_params = OrderServiceUpdate {
            filter,
            update_data: serde_json::Value::Object(update_data),
        };
        let update_json = serde_json::to_string(&update_params).unwrap();
        
        let result = db.update_order_service(update_json);
        assert!(result.is_ok());
        
        // Verify update
        let updated_order_service = db.order_services.get(&"os_001".to_string()).unwrap();
        assert_eq!(updated_order_service.order_status, "completed");
    }

    #[glue::test]
    fn test_delete_order_service() {
        let mut db = VCloudDB::new();
        let order_service = create_test_order_service("os_001");
        let order_service_json = serde_json::to_string(&order_service).unwrap();
        
        // Insert order service
        db.insert_order_service(order_service_json).unwrap();
        assert!(db.order_services.contains(&"os_001".to_string()));
        
        // Delete order service
        let filter = OrderServiceFilter {
            ids: Some(vec!["os_001".to_string()]),
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_order_service(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion
        assert!(!db.order_services.contains(&"os_001".to_string()));
    }

    #[glue::test]
    fn test_delete_many_order_service() {
        let mut db = VCloudDB::new();
        
        // Insert test order services
        let order_services = vec![
            create_test_order_service("os_001"),
            {
                let mut os2 = create_test_order_service("os_002");
                os2.order_id = "order_002".to_string();
                os2.user_service_id = "user_service_002".to_string();
                os2
            },
            {
                let mut os3 = create_test_order_service("os_003");
                os3.order_id = "order_003".to_string();
                os3.user_service_id = "user_service_003".to_string();
                os3
            },
        ];
        
        for order_service in order_services {
            let order_service_json = serde_json::to_string(&order_service).unwrap();
            db.insert_order_service(order_service_json).unwrap();
        }
        
        // Delete multiple order services
        let filter = OrderServiceFilter {
            ids: Some(vec!["os_001".to_string(), "os_002".to_string()]),
            order_id: None,
            user_service_id: None,
            order_status: None,
            order_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_many_order_service(filter_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify deletions
        assert!(!db.order_services.contains(&"os_001".to_string()));
        assert!(!db.order_services.contains(&"os_002".to_string()));
        assert!(db.order_services.contains(&"os_003".to_string()));
    }
}
