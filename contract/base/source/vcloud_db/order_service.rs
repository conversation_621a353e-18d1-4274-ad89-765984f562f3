// Order Service module implementation

impl VCloudDB {
    /// Create a new order service from JSON string
    pub fn insert_order_service(&mut self, order_service_json: String) -> anyhow::Result<String> {
        let mut order_service: OrderService = serde_json::from_str(&order_service_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order service JSON: {}", e))?;

        // Validate required fields
        if order_service._id.is_empty() {
            return Err(anyhow::anyhow!("Order service ID cannot be empty"));
        }

        if self.order_services.contains(&order_service._id) {
            return Err(anyhow::anyhow!("Order service with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_order_service_timestamp_handling(&mut order_service);

        // Ensure deleted_at is 0 for new order services
        order_service.deleted_at = 0;

        self.order_services.insert(&order_service._id, &order_service);
        Ok(order_service._id)
    }

    /// Internal implementation for batch inserting order services
    pub(crate) fn insert_many_order_service(&mut self, insert_many_json_string: String) -> anyhow::Result<String> {
        // Parse JSON input with detailed error reporting
        let order_services: Vec<OrderService> = serde_json::from_str(&insert_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse order services JSON: {}. Expected format: [{{\"_id\": \"os1\", \"orderID\": \"order1\", ...}}, ...]", e))?;

        // Validate input is not empty
        if order_services.is_empty() {
            return Err(anyhow::anyhow!("Order services array cannot be empty"));
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut order_service in order_services {
            // Validate required fields
            if order_service._id.is_empty() {
                result.errors.push("Order service ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicates within the batch
            if batch_ids.contains(&order_service._id) {
                result.errors.push(format!("Duplicate order service ID in batch: {}", order_service._id));
                continue;
            }
            batch_ids.insert(order_service._id.clone());

            // Check if order service already exists in database
            if self.order_services.contains(&order_service._id) {
                result.errors.push(format!("Order service '{}' already exists", order_service._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_order_service_timestamp_handling(&mut order_service);

            // Ensure deleted_at is 0 for new order services
            order_service.deleted_at = 0;

            // Insert the order service
            self.order_services.insert(&order_service._id, &order_service);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single order service by ID
    pub fn get_order_service(&self, id: String) -> anyhow::Result<String> {
        let order_service = self.order_services.get(&id);
        match order_service {
            Some(order_service) => Ok(serde_json::to_string(&order_service)?),
            None => Err(anyhow::anyhow!("Order service not found")),
        }
    }

    /// Apply timestamp handling logic for order services
    pub(crate) fn apply_order_service_timestamp_handling(&self, order_service: &mut OrderService) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if order_service.created_at == 0 {
            order_service.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if order_service.updated_at == 0 {
            order_service.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update order services with advanced filtering
    pub fn update_order_service(&mut self, update_json_string: String) -> anyhow::Result<String> {
        let update_params: OrderServiceUpdate = serde_json::from_str(&update_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceUpdate JSON: {}", e))?;

        let mut updated_count = 0u64;

        // Find order services to update
        let order_services_to_update = self.find_order_services_for_update(&update_params.filter)?;

        for mut order_service in order_services_to_update {
            // Apply updates from update_data
            if let Some(order_id) = update_params.update_data.get("orderID").and_then(|v| v.as_str()) {
                order_service.order_id = order_id.to_string();
            }
            if let Some(user_service_id) = update_params.update_data.get("userServiceID").and_then(|v| v.as_str()) {
                order_service.user_service_id = user_service_id.to_string();
            }
            if let Some(order_status) = update_params.update_data.get("orderStatus").and_then(|v| v.as_str()) {
                order_service.order_status = order_status.to_string();
            }
            if let Some(order_type) = update_params.update_data.get("orderType").and_then(|v| v.as_str()) {
                order_service.order_type = order_type.to_string();
            }

            // Always update the updated_at timestamp
            order_service.updated_at = self.get_current_timestamp();

            // Update in storage
            self.order_services.insert(&order_service._id, &order_service);
            updated_count += 1;
        }

        Ok(format!("{{\"updated\": {}}}", updated_count))
    }

    /// Update many order services with advanced filtering
    pub fn update_many_order_service(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
        let updates: Vec<OrderServiceUpdate> = serde_json::from_str(&update_many_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse order service updates JSON: {}", e))?;

        let mut total_updated = 0u64;
        let mut errors = Vec::new();

        for update in updates {
            match self.update_order_service(serde_json::to_string(&update)?) {
                Ok(result_str) => {
                    if let Ok(result) = serde_json::from_str::<serde_json::Value>(&result_str) {
                        if let Some(updated) = result.get("updated").and_then(|v| v.as_u64()) {
                            total_updated += updated;
                        }
                    }
                }
                Err(e) => {
                    errors.push(format!("Update failed: {}", e));
                }
            }
        }

        let result = BatchResult {
            created: 0,
            updated: total_updated,
            deleted: 0,
            errors,
        };

        Ok(serde_json::to_string(&result)?)
    }

    /// Helper function to find order services for update operations
    fn find_order_services_for_update(&self, filter: &OrderServiceFilter) -> anyhow::Result<Vec<OrderService>> {
        let mut order_services = Vec::new();

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = filter.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, filter) {
                        order_services.push(order_service);
                    }
                }
            }
        } else if let Some(ref user_service_id) = filter.user_service_id {
            // Use user_service_id index
            self.query_order_service_with_index("order_services_user_service_id", user_service_id, filter, &mut order_services)?;
        } else if let Some(ref order_id) = filter.order_id {
            // Use order_id index
            self.query_order_service_with_index("order_services_order_id", order_id, filter, &mut order_services)?;
        } else if let Some(ref order_type) = filter.order_type {
            // Use order_type index
            self.query_order_service_with_index("order_services_order_type", order_type, filter, &mut order_services)?;
        } else if let Some(ref order_status) = filter.order_status {
            // Use order_status index
            self.query_order_service_with_index("order_services_order_status", order_status, filter, &mut order_services)?;
        } else {
            // Use created_at index for general queries
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, filter) {
                    order_services.push(order_service);
                }
            }
        }

        Ok(order_services)
    }

    /// Helper function to query order services with a specific index
    fn query_order_service_with_index(
        &self,
        index_name: &str,
        key: &str,
        filter: &OrderServiceFilter,
        order_services: &mut Vec<OrderService>,
    ) -> anyhow::Result<()> {
        let key_prefix = format!("{}-", key);
        let mut iter = self.order_services.index(index_name).iter(
            false,
            &key_prefix,
            &format!("{}~", key_prefix)
        );

        while let Some((_, order_service)) = iter.next() {
            if self.matches_order_service_filters(&order_service, filter) {
                order_services.push(order_service);
            }
        }

        Ok(())
    }

    /// Internal implementation for deleting a single order service (HARD DELETE)
    pub(crate) fn delete_order_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderServiceFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceFilter JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.order_services.contains(id) {
                    // Hard delete: completely remove from storage
                    self.order_services.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching order service
        let mut iter = self.order_services.index("order_services_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while let Some((_, order_service)) = iter.next() {
            if self.matches_order_service_filters(&order_service, &params) {
                // Hard delete: completely remove from storage
                self.order_services.remove(&order_service._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Delete many order services with advanced filtering (HARD DELETE)
    pub fn delete_many_order_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderServiceFilter = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceFilter JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Collect order services to delete
        let mut order_services_to_delete = Vec::new();

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        order_services_to_delete.push(order_service);
                    }
                }
            }
        } else if let Some(ref user_service_id) = params.user_service_id {
            // Use user_service_id index
            self.query_order_service_with_index("order_services_user_service_id", user_service_id, &params, &mut order_services_to_delete)?;
        } else if let Some(ref order_id) = params.order_id {
            // Use order_id index
            self.query_order_service_with_index("order_services_order_id", order_id, &params, &mut order_services_to_delete)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index
            self.query_order_service_with_index("order_services_order_type", order_type, &params, &mut order_services_to_delete)?;
        } else if let Some(ref order_status) = params.order_status {
            // Use order_status index
            self.query_order_service_with_index("order_services_order_status", order_status, &params, &mut order_services_to_delete)?;
        } else {
            // Use created_at index for general queries
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    order_services_to_delete.push(order_service);
                }
            }
        }

        // Delete each matching order service (HARD DELETE)
        for order_service in order_services_to_delete {
            // Hard delete: completely remove from storage
            self.order_services.remove(&order_service._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query order services with comprehensive filtering, pagination, and sorting
    pub fn find_order_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderServiceFilter = serde_json::from_str(&params_json)?;

        let mut order_services = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        if count >= offset && order_services.len() < limit as usize {
                            order_services.push(order_service);
                        }
                        count += 1;
                    }
                }
            }
        } else if let Some(ref user_service_id) = params.user_service_id {
            // Use user_service_id index with efficient sorting
            self.query_order_service_with_index_paginated("order_services_user_service_id", user_service_id, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_id) = params.order_id {
            // Use order_id index with efficient sorting
            self.query_order_service_with_index_paginated("order_services_order_id", order_id, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index with efficient sorting
            self.query_order_service_with_index_paginated("order_services_order_type", order_type, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_status) = params.order_status {
            // Use order_status index with efficient sorting
            self.query_order_service_with_index_paginated("order_services_order_status", order_status, &params, &mut order_services, &mut count, limit, offset)?;
        } else {
            // Use created_at index with efficient sorting
            self.query_order_service_with_created_at(&params, &mut order_services, &mut count, limit, offset)?;
        }

        Ok(serde_json::to_string(&order_services)?)
    }

    /// Count order services with advanced filtering
    pub fn count_order_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderServiceFilter = serde_json::from_str(&params_json)?;
        let mut count = 0u64;

        // Use direct ID lookup if IDs are provided
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        count += 1;
                    }
                }
            }
        } else if let Some(ref user_service_id) = params.user_service_id {
            // Use user_service_id index
            let key_prefix = format!("{}-", user_service_id);
            let mut iter = self.order_services.index("order_services_user_service_id").iter(
                false,
                &key_prefix,
                &format!("{}~", key_prefix)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        } else if let Some(ref order_id) = params.order_id {
            // Use order_id index
            let key_prefix = format!("{}-", order_id);
            let mut iter = self.order_services.index("order_services_order_id").iter(
                false,
                &key_prefix,
                &format!("{}~", key_prefix)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index
            let key_prefix = format!("{}-", order_type);
            let mut iter = self.order_services.index("order_services_order_type").iter(
                false,
                &key_prefix,
                &format!("{}~", key_prefix)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        } else if let Some(ref order_status) = params.order_status {
            // Use order_status index
            let key_prefix = format!("{}-", order_status);
            let mut iter = self.order_services.index("order_services_order_status").iter(
                false,
                &key_prefix,
                &format!("{}~", key_prefix)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        } else {
            // Use created_at index for general queries
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:0>19}", i64::MAX)
            );

            while let Some((_, order_service)) = iter.next() {
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        }

        Ok(format!("{{\"count\": {}}}", count))
    }

    /// Helper function to check if an order service matches the given filters
    pub(crate) fn matches_order_service_filters(&self, order_service: &OrderService, params: &OrderServiceFilter) -> bool {
        // Skip deleted order services unless specifically querying for them
        if order_service.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&order_service._id) {
                return false;
            }
        }

        // Check order_id filter
        if let Some(ref order_id) = params.order_id {
            if order_service.order_id != *order_id {
                return false;
            }
        }

        // Check user_service_id filter
        if let Some(ref user_service_id) = params.user_service_id {
            if order_service.user_service_id != *user_service_id {
                return false;
            }
        }

        // Check order_status filter
        if let Some(ref order_status) = params.order_status {
            if order_service.order_status != *order_status {
                return false;
            }
        }

        // Check order_type filter
        if let Some(ref order_type) = params.order_type {
            if order_service.order_type != *order_type {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if order_service.created_at < start {
                return false;
            }
        }

        if let Some(end) = params.created_at_end {
            if order_service.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if order_service.updated_at < start {
                return false;
            }
        }

        if let Some(end) = params.updated_at_end {
            if order_service.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Helper function to query order services using created_at index with efficient sorting
    fn query_order_service_with_created_at(
        &self,
        params: &OrderServiceFilter,
        order_services: &mut Vec<OrderService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        let sort_desc = params.sort_desc.unwrap_or(true);

        let mut iter = self.order_services.index("order_services_created_at").iter(
            sort_desc,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while let Some((_, order_service)) = iter.next() {
            if self.matches_order_service_filters(&order_service, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if order_services.len() >= limit as usize {
                    break;
                }
                order_services.push(order_service);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Helper function to query order services with a specific index and pagination
    fn query_order_service_with_index_paginated(
        &self,
        index_name: &str,
        key: &str,
        params: &OrderServiceFilter,
        order_services: &mut Vec<OrderService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        let sort_desc = params.sort_desc.unwrap_or(true);
        let key_prefix = format!("{}-", key);

        let mut iter = self.order_services.index(index_name).iter(
            sort_desc,
            &key_prefix,
            &format!("{}~", key_prefix)
        );

        while let Some((_, order_service)) = iter.next() {
            if self.matches_order_service_filters(&order_service, params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if order_services.len() >= limit as usize {
                    break;
                }
                order_services.push(order_service);
                *count += 1;
            }
        }

        Ok(())
    }
}
