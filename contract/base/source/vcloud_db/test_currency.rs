#[cfg(test)]
mod currency_tests {
    use super::*;

    fn create_test_currency(id: &str) -> Currency {
        Currency {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            name_or_id: "Bitcoin".to_string(),
            contract_id: "0xbtc_contract".to_string(),
            symbol_name: "BTC".to_string(),
            contract_type: "ERC20".to_string(),
            unit: 8,
            exchange_rate: 50000.0,
        }
    }

    #[glue::test]
    fn test_insert_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("btc_001");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        let result = db.insert_currency(currency_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "btc_001");
        
        // Verify currency was inserted
        assert!(db.currencies.contains(&"btc_001".to_string()));
    }

    #[glue::test]
    fn test_insert_currency_duplicate() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("btc_001");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert first time - should succeed
        let result1 = db.insert_currency(currency_json.clone());
        assert!(result1.is_ok());
        
        // Insert second time - should fail
        let result2 = db.insert_currency(currency_json);
        assert!(result2.is_err());
        assert!(result2.unwrap_err().to_string().contains("already exists"));
    }

    #[glue::test]
    fn test_insert_many_currency() {
        let mut db = VCloudDB::new();
        let currencies = vec![
            create_test_currency("btc_001"),
            {
                let mut eth = create_test_currency("eth_001");
                eth.name_or_id = "Ethereum".to_string();
                eth.symbol_name = "ETH".to_string();
                eth.exchange_rate = 3000.0;
                eth
            },
            {
                let mut usdt = create_test_currency("usdt_001");
                usdt.name_or_id = "Tether".to_string();
                usdt.symbol_name = "USDT".to_string();
                usdt.exchange_rate = 1.0;
                usdt
            },
        ];
        let currencies_json = serde_json::to_string(&currencies).unwrap();
        
        let result = db.insert_many_currency(currencies_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 3);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify all currencies were inserted
        assert!(db.currencies.contains(&"btc_001".to_string()));
        assert!(db.currencies.contains(&"eth_001".to_string()));
        assert!(db.currencies.contains(&"usdt_001".to_string()));
    }

    #[glue::test]
    fn test_get_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("btc_001");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency
        db.insert_currency(currency_json).unwrap();
        
        // Get currency
        let result = db.get_currency("btc_001".to_string());
        assert!(result.is_ok());
        
        let retrieved_currency: Currency = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_currency._id, "btc_001");
        assert_eq!(retrieved_currency.symbol_name, "BTC");
    }

    #[glue::test]
    fn test_get_currency_not_found() {
        let db = VCloudDB::new();
        let result = db.get_currency("nonexistent".to_string());
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("not found"));
    }

    #[glue::test]
    fn test_find_currency() {
        let mut db = VCloudDB::new();
        
        // Insert test currencies
        let currencies = vec![
            create_test_currency("btc_001"),
            {
                let mut eth = create_test_currency("eth_001");
                eth.name_or_id = "Ethereum".to_string();
                eth.symbol_name = "ETH".to_string();
                eth
            },
        ];
        
        for currency in currencies {
            let currency_json = serde_json::to_string(&currency).unwrap();
            db.insert_currency(currency_json).unwrap();
        }
        
        // Test find with empty filter
        let filter = CurrencyFilter {
            ids: None,
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_currency(filter_json);
        assert!(result.is_ok());
        
        let found_currencies: Vec<Currency> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_currencies.len(), 2);
    }

    #[glue::test]
    fn test_find_currency_by_symbol() {
        let mut db = VCloudDB::new();
        
        // Insert test currencies
        let currencies = vec![
            create_test_currency("btc_001"),
            {
                let mut eth = create_test_currency("eth_001");
                eth.name_or_id = "Ethereum".to_string();
                eth.symbol_name = "ETH".to_string();
                eth
            },
        ];
        
        for currency in currencies {
            let currency_json = serde_json::to_string(&currency).unwrap();
            db.insert_currency(currency_json).unwrap();
        }
        
        // Test find by symbol name
        let filter = CurrencyFilter {
            ids: None,
            name_or_id: None,
            contract_id: None,
            symbol_name: Some("BTC".to_string()),
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.find_currency(filter_json);
        assert!(result.is_ok());
        
        let found_currencies: Vec<Currency> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_currencies.len(), 1);
        assert_eq!(found_currencies[0].symbol_name, "BTC");
    }

    #[glue::test]
    fn test_update_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("btc_001");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency
        db.insert_currency(currency_json).unwrap();
        
        // Update currency
        let filter = CurrencyFilter {
            ids: Some(vec!["btc_001".to_string()]),
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        
        let mut update_data = serde_json::Map::new();
        update_data.insert("exchangeRate".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(60000.0).unwrap()));
        
        let update_params = CurrencyUpdate {
            filter,
            update_data: serde_json::Value::Object(update_data),
        };
        let update_json = serde_json::to_string(&update_params).unwrap();
        
        let result = db.update_currency(update_json);
        assert!(result.is_ok());
        
        // Verify update
        let updated_currency = db.currencies.get(&"btc_001".to_string()).unwrap();
        assert_eq!(updated_currency.exchange_rate, 60000.0);
    }

    #[glue::test]
    fn test_delete_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("btc_001");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency
        db.insert_currency(currency_json).unwrap();
        assert!(db.currencies.contains(&"btc_001".to_string()));
        
        // Delete currency
        let filter = CurrencyFilter {
            ids: Some(vec!["btc_001".to_string()]),
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_currency(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion
        assert!(!db.currencies.contains(&"btc_001".to_string()));
    }

    #[glue::test]
    fn test_delete_many_currency() {
        let mut db = VCloudDB::new();
        
        // Insert test currencies
        let currencies = vec![
            create_test_currency("btc_001"),
            {
                let mut eth = create_test_currency("eth_001");
                eth.name_or_id = "Ethereum".to_string();
                eth.symbol_name = "ETH".to_string();
                eth
            },
            {
                let mut usdt = create_test_currency("usdt_001");
                usdt.name_or_id = "Tether".to_string();
                usdt.symbol_name = "USDT".to_string();
                usdt
            },
        ];
        
        for currency in currencies {
            let currency_json = serde_json::to_string(&currency).unwrap();
            db.insert_currency(currency_json).unwrap();
        }
        
        // Delete multiple currencies
        let filter = CurrencyFilter {
            ids: Some(vec!["btc_001".to_string(), "eth_001".to_string()]),
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_many_currency(filter_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify deletions
        assert!(!db.currencies.contains(&"btc_001".to_string()));
        assert!(!db.currencies.contains(&"eth_001".to_string()));
        assert!(db.currencies.contains(&"usdt_001".to_string()));
    }
}
