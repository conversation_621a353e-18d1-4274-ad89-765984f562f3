#!/usr/bin/env python3
import re

def fix_iterators(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace the iterator pattern
    pattern = r'while let Some\(\(_, (\w+)\)\) = iter\.next\(\) \{'
    replacement = r'while iter.next() {\n            let \1 = iter.value()?;'
    
    content = re.sub(pattern, replacement, content)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed iterators in {file_path}")

if __name__ == "__main__":
    fix_iterators("contract/base/source/vcloud_db/order_service.rs")
